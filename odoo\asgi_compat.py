# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
ASGI Compatibility Layer

This module provides compatibility between the new ASGI implementation and
existing WSGI-based controllers and addons to ensure smooth transition.
"""

import asyncio
import logging
import threading
from contextlib import contextmanager
from typing import Any, Dict, Optional, Callable

from werkzeug.wrappers import Response as WerkzeugResponse
from starlette.responses import Response as StarletteResponse

from odoo import http as odoo_http
from odoo.http import request as current_request
from odoo.modules.registry import Registry

_logger = logging.getLogger(__name__)


class WSGIToASGIAdapter:
    """
    Adapter to run WSGI applications within ASGI context
    """
    
    def __init__(self, wsgi_app):
        self.wsgi_app = wsgi_app
    
    async def __call__(self, scope, receive, send):
        """
        ASGI application interface that wraps WSGI app
        """
        if scope['type'] != 'http':
            # Only handle HTTP requests
            await send({
                'type': 'http.response.start',
                'status': 404,
                'headers': [[b'content-type', b'text/plain']],
            })
            await send({
                'type': 'http.response.body',
                'body': b'Not Found',
            })
            return
        
        # Convert ASGI scope to WSGI environ
        environ = self._asgi_to_wsgi_environ(scope, receive)
        
        # Create response collector
        response_data = {'status': None, 'headers': [], 'body': b''}
        
        def start_response(status, headers, exc_info=None):
            response_data['status'] = int(status.split(' ', 1)[0])
            response_data['headers'] = headers
        
        # Run WSGI app in thread pool
        loop = asyncio.get_event_loop()
        try:
            result = await loop.run_in_executor(
                None, 
                lambda: list(self.wsgi_app(environ, start_response))
            )
            response_data['body'] = b''.join(result)
        except Exception as e:
            _logger.exception("Error in WSGI adapter")
            response_data['status'] = 500
            response_data['headers'] = [('Content-Type', 'text/plain')]
            response_data['body'] = b'Internal Server Error'
        
        # Send ASGI response
        await send({
            'type': 'http.response.start',
            'status': response_data['status'],
            'headers': [[k.encode(), v.encode()] for k, v in response_data['headers']],
        })
        await send({
            'type': 'http.response.body',
            'body': response_data['body'],
        })
    
    def _asgi_to_wsgi_environ(self, scope, receive):
        """
        Convert ASGI scope to WSGI environ dict
        """
        environ = {
            'REQUEST_METHOD': scope['method'],
            'SCRIPT_NAME': '',
            'PATH_INFO': scope['path'],
            'QUERY_STRING': scope.get('query_string', b'').decode('latin1'),
            'CONTENT_TYPE': '',
            'CONTENT_LENGTH': '',
            'SERVER_NAME': scope.get('server', ['localhost', None])[0],
            'SERVER_PORT': str(scope.get('server', [None, 80])[1]),
            'SERVER_PROTOCOL': f"HTTP/{scope['http_version']}",
            'wsgi.version': (1, 0),
            'wsgi.url_scheme': scope.get('scheme', 'http'),
            'wsgi.input': None,  # Will be set later
            'wsgi.errors': None,
            'wsgi.multithread': True,
            'wsgi.multiprocess': False,
            'wsgi.run_once': False,
        }
        
        # Add headers
        for header_name, header_value in scope.get('headers', []):
            name = header_name.decode('latin1')
            value = header_value.decode('latin1')
            
            if name.lower() == 'content-type':
                environ['CONTENT_TYPE'] = value
            elif name.lower() == 'content-length':
                environ['CONTENT_LENGTH'] = value
            else:
                # Convert to CGI-style environ key
                key = f"HTTP_{name.upper().replace('-', '_')}"
                environ[key] = value
        
        # Add client info
        if 'client' in scope:
            environ['REMOTE_ADDR'] = scope['client'][0]
            environ['REMOTE_PORT'] = str(scope['client'][1])
        
        return environ


class RequestCompatibilityWrapper:
    """
    Wrapper to make ASGI requests compatible with existing Odoo request interface
    """
    
    def __init__(self, asgi_request):
        self.asgi_request = asgi_request
        self._setup_compatibility()
    
    def _setup_compatibility(self):
        """Setup compatibility attributes"""
        # Copy all attributes from ASGI request
        for attr in dir(self.asgi_request):
            if not attr.startswith('_') and not callable(getattr(self.asgi_request, attr)):
                setattr(self, attr, getattr(self.asgi_request, attr))
    
    def __getattr__(self, name):
        """Delegate unknown attributes to ASGI request"""
        return getattr(self.asgi_request, name)


class ResponseCompatibilityWrapper:
    """
    Wrapper to convert between different response types
    """
    
    @staticmethod
    def convert_to_starlette(response):
        """Convert various response types to Starlette response"""
        if isinstance(response, StarletteResponse):
            return response
        elif isinstance(response, WerkzeugResponse):
            return StarletteResponse(
                content=response.get_data(),
                status_code=response.status_code,
                headers=dict(response.headers)
            )
        elif isinstance(response, odoo_http.Response):
            return StarletteResponse(
                content=response.data,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
        elif isinstance(response, str):
            return StarletteResponse(content=response)
        elif response is None:
            return StarletteResponse(content="")
        else:
            return StarletteResponse(content=str(response))


class ThreadLocalCompatibility:
    """
    Maintain thread-local compatibility for existing code
    """
    
    @staticmethod
    @contextmanager
    def request_context(asgi_request):
        """
        Context manager to setup thread-local request context
        """
        # Setup thread-local variables
        thread = threading.current_thread()
        old_request = getattr(thread, 'request', None)
        
        try:
            # Set new request
            thread.request = RequestCompatibilityWrapper(asgi_request)
            
            # Setup other thread-local variables
            thread.query_count = 0
            thread.query_time = 0
            thread.perf_t0 = 0
            thread.cursor_mode = None
            
            yield thread.request
            
        finally:
            # Restore old request
            if old_request is not None:
                thread.request = old_request
            else:
                delattr(thread, 'request')


class ControllerCompatibility:
    """
    Compatibility layer for existing controllers
    """
    
    @staticmethod
    async def call_controller_method(method, *args, **kwargs):
        """
        Call controller method with compatibility handling
        """
        # Check if method is async
        if asyncio.iscoroutinefunction(method):
            return await method(*args, **kwargs)
        else:
            # Run sync method in thread pool
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: method(*args, **kwargs))
    
    @staticmethod
    def wrap_sync_controller(controller_class):
        """
        Wrap synchronous controller to work with ASGI
        """
        class AsyncControllerWrapper(controller_class):
            async def __call__(self, *args, **kwargs):
                return await ControllerCompatibility.call_controller_method(
                    super().__call__, *args, **kwargs
                )
        
        return AsyncControllerWrapper


class DatabaseCompatibility:
    """
    Database compatibility layer for async operations
    """

    @staticmethod
    async def get_registry(db_name):
        """
        Get database registry asynchronously using async database connections
        """
        try:
            # Try to use async database connections if available
            from odoo.async_sql_db import async_db_connect

            # For now, still use thread pool for registry operations
            # as the registry system is deeply synchronous
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: Registry(db_name)
            )
        except ImportError:
            # Fallback to thread pool if async_sql_db is not available
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: Registry(db_name)
            )

    @staticmethod
    async def execute_in_env(env, func, *args, **kwargs):
        """
        Execute function in database environment asynchronously
        """
        # For now, still use thread pool as the ORM is synchronous
        # In the future, this could be enhanced to use async database operations
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: func(*args, **kwargs)
        )

    @staticmethod
    async def get_async_cursor(db_name, readonly=False):
        """
        Get an async database cursor
        """
        from odoo.async_sql_db import async_db_connect

        connection = await async_db_connect(db_name, readonly=readonly)
        return connection.cursor()

    @staticmethod
    async def execute_async_query(db_name, query, params=None, readonly=True):
        """
        Execute a query asynchronously and return results
        """
        cursor = await DatabaseCompatibility.get_async_cursor(db_name, readonly=readonly)
        try:
            async with cursor:
                if params:
                    await cursor.execute(query, params)
                else:
                    await cursor.execute(query)
                return await cursor.fetchall()
        finally:
            await cursor.close()

    @staticmethod
    async def execute_async_transaction(db_name, operations):
        """
        Execute multiple operations in an async transaction

        Args:
            db_name: Database name
            operations: List of (query, params) tuples
        """
        cursor = await DatabaseCompatibility.get_async_cursor(db_name, readonly=False)
        try:
            async with cursor:
                for query, params in operations:
                    await cursor.execute(query, params)
                await cursor.commit()
        except Exception:
            await cursor.rollback()
            raise
        finally:
            await cursor.close()


class SessionCompatibility:
    """
    Session compatibility layer
    """
    
    @staticmethod
    async def load_session(session_id):
        """Load session asynchronously"""
        # Placeholder for async session loading
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: {}  # Return empty session for now
        )
    
    @staticmethod
    async def save_session(session_id, session_data):
        """Save session asynchronously"""
        # Placeholder for async session saving
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: None
        )


# Global compatibility functions
def ensure_async(func):
    """
    Decorator to ensure function is async-compatible
    """
    if asyncio.iscoroutinefunction(func):
        return func
    else:
        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: func(*args, **kwargs))
        return async_wrapper


def run_sync_in_async(func, *args, **kwargs):
    """
    Run synchronous function in async context
    """
    loop = asyncio.get_event_loop()
    return loop.run_in_executor(None, lambda: func(*args, **kwargs))


# Monkey patch for backward compatibility
def patch_http_module():
    """
    Monkey patch the http module for backward compatibility
    """
    # Add async versions of common functions
    odoo_http.ensure_async = ensure_async
    odoo_http.run_sync_in_async = run_sync_in_async

    # Add compatibility wrappers
    odoo_http.RequestCompatibilityWrapper = RequestCompatibilityWrapper
    odoo_http.ResponseCompatibilityWrapper = ResponseCompatibilityWrapper
    odoo_http.ThreadLocalCompatibility = ThreadLocalCompatibility
